<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手项目图表集合</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .chart-container {
            margin: 40px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #fafafa;
            page-break-inside: avoid;
        }
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 5px;
            min-height: 400px;
        }
        .description {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .description h4 {
            margin-top: 0;
            color: #2980b9;
        }
        .tech-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .tech-spec {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #bdc3c7;
        }
        .tech-spec strong {
            color: #e74c3c;
        }
        .btn-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
        .usage-note {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能助手项目图表集合</h1>
        
        <div class="btn-container">
            <p><strong>📋 使用说明：</strong></p>
            <p>1. 右键点击图表区域选择"另存为图像"</p>
            <p>2. 使用浏览器截图功能（Ctrl+Shift+S）</p>
            <p>3. 使用第三方截图工具保存图片</p>
        </div>

        <!-- 图表1：系统架构图 -->
        <div class="chart-container" id="chart1">
            <div class="chart-title">🏗️ 1. 系统架构图</div>
            <div class="mermaid">
graph TB
    subgraph "用户界面层"
        A[Streamlit Web界面]
        B[功能选择面板]
        C[文件上传组件]
    end
    
    subgraph "业务逻辑层"
        D[聊天机器人模块]
        E[知识库问答模块]
        F[智能翻译模块]
        G[智能客服模块]
        H[竞赛信息提取模块]
    end
    
    subgraph "服务层"
        I[讯飞星火4.0Ultra<br/>大语言模型]
        J[text2vec-base-chinese<br/>嵌入模型]
        K[ChromaDB<br/>向量数据库]
        L[超时控制服务<br/>120秒保护]
    end
    
    subgraph "数据存储层"
        M[data/knowledge<br/>文档存储]
        N[data/vector_db<br/>向量存储]
        O[local_models<br/>模型文件]
        P[static/styles<br/>样式资源]
    end
    
    A --> B
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    C --> E
    C --> H
    
    D --> I
    E --> J
    E --> K
    F --> I
    G --> I
    H --> I
    
    D --> L
    E --> L
    F --> L
    G --> L
    H --> L
    
    J --> O
    K --> N
    E --> M
    A --> P
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style I fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style K fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style J fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style L fill:#ffebee,stroke:#b71c1c,stroke-width:2px
            </div>
            <div class="description">
                <h4>📊 架构说明</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>用户界面层:</strong> Streamlit Web界面 + 功能选择面板</div>
                    <div class="tech-spec"><strong>业务逻辑层:</strong> 5大功能模块独立设计</div>
                    <div class="tech-spec"><strong>服务层:</strong> 讯飞星火4.0Ultra + 向量数据库</div>
                    <div class="tech-spec"><strong>数据存储层:</strong> 本地化存储 + 模型文件管理</div>
                </div>
            </div>
        </div>

        <!-- 图表2：安全机制流程图 -->
        <div class="chart-container" id="chart2">
            <div class="chart-title">🔒 2. 安全机制与超时控制流程图</div>
            <div class="mermaid">
graph TD
    A[用户输入] --> B{输入验证}
    B -->|长度检查| C{≤3500字符?}
    B -->|内容过滤| D{安全检查}
    
    C -->|否| E[智能预处理<br/>关键信息提取]
    C -->|是| F[超时装饰器<br/>@timeout 120s]
    D -->|通过| F
    D -->|拦截| G[安全提示]
    E --> F
    
    F --> H[LLM调用<br/>讯飞星火4.0Ultra]
    H --> I{调用状态}
    
    I -->|成功| J[返回结果]
    I -->|超时| K[TimeoutError<br/>优雅降级]
    I -->|异常| L[Exception处理<br/>错误日志]
    
    K --> M[友好错误提示<br/>建议简化问题]
    L --> N[详细错误信息<br/>调试支持]
    
    subgraph "跨平台超时实现"
        O[Windows: 线程模式<br/>Thread + join timeout]
        P[Unix/Linux: 信号模式<br/>signal.SIGALRM]
    end
    
    F --> O
    F --> P
    
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style B fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    style F fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style H fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style K fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style L fill:#fce4ec,stroke:#c2185b,stroke-width:2px
            </div>
            <div class="description">
                <h4>🛡️ 安全特性</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>输入验证:</strong> 长度检查(≤3500字符) + 内容安全过滤</div>
                    <div class="tech-spec"><strong>超时保护:</strong> 120秒全局超时装饰器</div>
                    <div class="tech-spec"><strong>跨平台实现:</strong> Windows线程模式 + Unix信号模式</div>
                    <div class="tech-spec"><strong>错误处理:</strong> TimeoutError优雅降级 + Exception详细日志</div>
                </div>
            </div>
        </div>

        <!-- 图表3：RAG知识库问答流程图 -->
        <div class="chart-container" id="chart3">
            <div class="chart-title">📚 3. RAG知识库问答系统流程图</div>
            <div class="mermaid">
graph TD
    subgraph "文档处理阶段"
        A[用户上传文档<br/>PDF/Word/TXT/MD] --> B[万能文档加载器<br/>UniversalTextLoader]
        B --> C[多编码识别<br/>UTF-8/GBK/UTF-16/ISO-8859-1]
        C --> D[文本分割<br/>chunk_size=500<br/>overlap=100]
        D --> E[text2vec嵌入<br/>GPU/CPU自适应]
        E --> F[ChromaDB存储<br/>持久化向量索引]
    end
    
    subgraph "问答查询阶段"
        G[用户提问] --> H[问题嵌入<br/>text2vec-base-chinese]
        H --> I[向量相似性检索<br/>similarity_search k=3]
        I --> J[上下文构建<br/>来源+内容组合]
        J --> K[讯飞星火生成<br/>基于上下文回答]
        K --> L[返回专业答案]
    end
    
    subgraph "技术优化"
        M[GPU加速计算<br/>CUDA 11.8+]
        N[超时保护<br/>@timeout 120s]
        O[错误处理<br/>优雅降级]
    end
    
    F --> I
    E --> M
    K --> N
    L --> O
    
    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    style E fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style K fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style M fill:#ffebee,stroke:#c62828,stroke-width:2px
            </div>
            <div class="description">
                <h4>🔍 技术实现</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>文档处理:</strong> 万能文档加载器 + 多编码识别</div>
                    <div class="tech-spec"><strong>文本分割:</strong> chunk_size=500, overlap=100</div>
                    <div class="tech-spec"><strong>向量化:</strong> text2vec-base-chinese + GPU/CPU自适应</div>
                    <div class="tech-spec"><strong>存储检索:</strong> ChromaDB持久化 + similarity_search(k=3)</div>
                    <div class="tech-spec"><strong>答案生成:</strong> 讯飞星火基于上下文生成专业回答</div>
                    <div class="tech-spec"><strong>准确率:</strong> >85% 问答准确度</div>
                </div>
            </div>
        </div>

        <div class="usage-note">
            <p><strong>💡 提示：</strong>图表已生成完成，请继续滚动查看剩余图表，或直接保存当前可见的图表。</p>
            <p><strong>建议文件名：</strong>system-architecture.png, security-mechanism.png, rag-knowledge-base.png</p>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                fontFamily: 'Microsoft YaHei, Arial, sans-serif',
                fontSize: '14px'
            }
        });

        // 页面加载完成后的处理
        window.addEventListener('load', function() {
            console.log('智能助手项目图表已加载完成');
            
            // 添加右键菜单提示
            document.addEventListener('contextmenu', function(e) {
                if (e.target.closest('.mermaid')) {
                    console.log('右键点击图表区域 - 可以保存图片');
                }
            });
        });
    </script>
</body>
</html>
