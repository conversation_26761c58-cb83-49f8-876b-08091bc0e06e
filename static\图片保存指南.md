# 📸 图片保存指南

## 🎯 概述

本指南将帮助您从HTML文件中保存Mermaid图表为图片文件，用于PPT制作。

---

## 📂 HTML文件说明

我已经为您生成了2个HTML文件：

1. **智能助手项目图表集合-修正版.html** - 包含前3个图表
   - 🏗️ 系统架构图
   - 🔒 安全机制与超时控制流程图  
   - 📚 RAG知识库问答系统流程图

2. **智能助手项目图表集合-第二部分.html** - 包含后2个图表
   - 🏆 竞赛信息提取智能处理流程图
   - 📊 技术成果与创新点展示图

---

## 🖼️ 保存图片的方法

### 方法1：浏览器右键保存（推荐）

1. 用浏览器打开HTML文件
2. 等待图表完全加载
3. 右键点击图表区域
4. 选择"另存为图像"或"复制图像"
5. 保存为PNG格式

### 方法2：浏览器截图功能

1. 打开HTML文件
2. 按 `F12` 打开开发者工具
3. 按 `Ctrl+Shift+P` 打开命令面板
4. 输入 "screenshot" 选择 "Capture full size screenshot"
5. 自动下载完整页面截图

### 方法3：第三方截图工具

1. 使用 Snipaste、微信截图等工具
2. 框选图表区域
3. 保存为PNG格式

---

## 📁 建议的文件命名和保存位置

### 文件命名规范

```
static/images/
├── system-architecture.png          # 系统架构图
├── security-mechanism.png           # 安全机制流程图
├── rag-knowledge-base.png           # RAG知识库问答流程图
├── competition-extraction.png       # 竞赛信息提取流程图
└── technical-achievements.png       # 技术成果展示图
```

### 在PPT中的使用

- **第1页 项目概述**: system-architecture.png
- **第2页 技术亮点**: security-mechanism.png  
- **第3页 知识库功能**: rag-knowledge-base.png
- **第4页 竞赛功能**: competition-extraction.png
- **第7页 成果展示**: technical-achievements.png

---

## 🔧 使用步骤

### 步骤1：打开HTML文件

```bash
# 方法1：直接双击HTML文件
# 方法2：在浏览器中打开
# 方法3：使用本地服务器
python -m http.server 8000
# 然后访问 http://localhost:8000/static/
```

### 步骤2：等待图表加载

- 确保网络连接正常（需要加载Mermaid.js）
- 等待所有图表完全渲染
- 图表应该显示为彩色的流程图

### 步骤3：保存图片

- 使用上述任一方法保存图片
- 建议保存为PNG格式，分辨率选择高质量
- 按照建议的命名规范保存

### 步骤4：更新MD文档

保存图片后，可以更新MD文档中的图片引用：

```markdown
![系统架构图](static/images/system-architecture.png)
![安全机制流程图](static/images/security-mechanism.png)
![RAG知识库问答流程图](static/images/rag-knowledge-base.png)
![竞赛信息提取流程图](static/images/competition-extraction.png)
![技术成果展示图](static/images/technical-achievements.png)
```

---

## ⚠️ 注意事项

### 图表质量

- 确保图表完全加载后再保存
- 选择高分辨率保存，适合PPT使用
- 如果图表显示不完整，刷新页面重试

### 浏览器兼容性

- 推荐使用 Chrome、Firefox、Edge 等现代浏览器
- 确保浏览器支持Mermaid.js渲染
- 如果图表不显示，检查网络连接

### 文件大小

- PNG格式适合PPT使用
- 如需压缩，可使用在线图片压缩工具
- 建议保持图片清晰度，文件大小适中

---

## 🎯 快速操作清单

- [ ] 打开 `智能助手项目图表集合-修正版.html`
- [ ] 保存前3个图表（system-architecture.png, security-mechanism.png, rag-knowledge-base.png）
- [ ] 打开 `智能助手项目图表集合-第二部分.html`  
- [ ] 保存后2个图表（competition-extraction.png, technical-achievements.png）
- [ ] 将图片保存到 `static/images/` 目录
- [ ] 更新PPT手册中的图片引用
- [ ] 开始制作PPT演示文稿

---

## 📞 问题排查

### 图表不显示

1. 检查网络连接
2. 刷新页面重试
3. 尝试不同浏览器
4. 检查浏览器控制台错误信息

### 保存失败

1. 尝试不同的保存方法
2. 检查浏览器权限设置
3. 使用截图工具作为备选方案

### 图片质量问题

1. 选择更高的分辨率
2. 使用浏览器的完整截图功能
3. 调整浏览器缩放比例后再保存

---

*图片保存指南完成，祝您PPT制作顺利！*
