<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手项目图表集合 - 第二部分</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        .chart-container {
            margin: 40px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #fafafa;
            page-break-inside: avoid;
        }
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 5px;
            min-height: 400px;
        }
        .description {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .description h4 {
            margin-top: 0;
            color: #2980b9;
        }
        .tech-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .tech-spec {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #bdc3c7;
        }
        .tech-spec strong {
            color: #e74c3c;
        }
        .btn-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
        .usage-note {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能助手项目图表集合 - 第二部分</h1>
        
        <div class="btn-container">
            <p><strong>📋 使用说明：</strong></p>
            <p>1. 右键点击图表区域选择"另存为图像"</p>
            <p>2. 使用浏览器截图功能（Ctrl+Shift+S）</p>
            <p>3. 使用第三方截图工具保存图片</p>
        </div>

        <!-- 图表4：竞赛信息提取流程图 -->
        <div class="chart-container" id="chart4">
            <div class="chart-title">🏆 4. 竞赛信息提取智能处理流程图</div>
            <div class="mermaid">
graph TD
    A[原始文档/文本输入] --> B[格式清理<br/>去除多余空格换行]
    B --> C[关键词筛选<br/>竞赛/比赛/报名/奖项/主办]
    C --> D{长度检查<br/>≤3500字符?}
    
    D -->|否| E[智能截取算法<br/>保留70%空间给提示词]
    D -->|是| F[构建结构化提示词]
    E --> F
    
    F --> G[讯飞星火LLM<br/>结构化信息提取]
    G --> H[JSON格式验证<br/>严格格式检查]
    
    H --> I{格式正确?}
    I -->|否| J[JSON修复算法<br/>去除markdown/单引号]
    I -->|是| K[返回结构化数据]
    J --> L{修复成功?}
    L -->|是| K
    L -->|否| M[返回错误信息<br/>提供解决方案]
    
    subgraph "输出结构"
        N[竞赛名称: 字符串]
        O[主办方: 字符串]
        P[关键日期: 对象<br/>报名截止/比赛时间]
        Q[核心要求: 数组]
        R[主要奖项: 数组]
    end
    
    K --> N
    K --> O
    K --> P
    K --> Q
    K --> R
    
    style A fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    style C fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style G fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style H fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style K fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    style M fill:#ffebee,stroke:#c62828,stroke-width:2px
            </div>
            <div class="description">
                <h4>🧠 智能处理流程</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>格式清理:</strong> 去除多余空格换行</div>
                    <div class="tech-spec"><strong>关键词筛选:</strong> 竞赛/比赛/报名/奖项/主办</div>
                    <div class="tech-spec"><strong>长度控制:</strong> ≤3500字符，智能截取保留70%空间</div>
                    <div class="tech-spec"><strong>结构化提取:</strong> 讯飞星火LLM生成JSON格式</div>
                    <div class="tech-spec"><strong>格式验证:</strong> JSON修复算法处理markdown/单引号</div>
                    <div class="tech-spec"><strong>压缩率:</strong> 70%智能压缩算法</div>
                </div>
            </div>
        </div>

        <!-- 图表5：技术成果展示图 -->
        <div class="chart-container" id="chart5">
            <div class="chart-title">📊 5. 技术成果与创新点展示图</div>
            <div class="mermaid">
graph TB
    subgraph "核心技术成果"
        A[功能完整性<br/>✅ 5大模块100%实现]
        B[性能优化<br/>✅ GPU加速+向量检索]
        C[安全机制<br/>✅ 120s超时全覆盖]
        D[兼容性<br/>✅ 跨平台Windows/Linux/Mac]
        E[部署便利<br/>✅ 本地化离线可用]
    end
    
    subgraph "技术创新亮点"
        F[智能预处理算法<br/>🚀 3500字符优化70%压缩率]
        G[多编码自适应<br/>🚀 UTF-8/GBK/UTF-16/ISO支持]
        H[装饰器超时控制<br/>🚀 跨平台信号/线程双模式]
        I[RAG架构设计<br/>🚀 向量检索+生成85%准确率]
        J[模块化松耦合<br/>🚀 6个工具模块独立可扩展]
    end
    
    subgraph "性能指标"
        K[响应时间: <5秒<br/>平均响应速度]
        L[准确率: >85%<br/>问答准确度]
        M[并发能力: 多用户<br/>同时使用支持]
        N[稳定性: 99%+<br/>系统可用性]
        O[文档支持: 4种格式<br/>PDF/Word/TXT/MD]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> J
    
    F --> K
    G --> L
    H --> M
    I --> N
    J --> O
    
    style A fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    style B fill:#2196f3,stroke:#1565c0,stroke-width:3px,color:#fff
    style C fill:#ff9800,stroke:#ef6c00,stroke-width:3px,color:#fff
    style D fill:#9c27b0,stroke:#7b1fa2,stroke-width:3px,color:#fff
    style E fill:#f44336,stroke:#c62828,stroke-width:3px,color:#fff
    
    style F fill:#81c784,stroke:#388e3c,stroke-width:2px
    style G fill:#64b5f6,stroke:#1976d2,stroke-width:2px
    style H fill:#ffb74d,stroke:#f57c00,stroke-width:2px
    style I fill:#ba68c8,stroke:#8e24aa,stroke-width:2px
    style J fill:#e57373,stroke:#d32f2f,stroke-width:2px
            </div>
            <div class="description">
                <h4>🎯 技术成果总结</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>功能完整性:</strong> 5大模块100%实现</div>
                    <div class="tech-spec"><strong>性能优化:</strong> GPU加速+向量检索</div>
                    <div class="tech-spec"><strong>安全机制:</strong> 120s超时全覆盖</div>
                    <div class="tech-spec"><strong>兼容性:</strong> 跨平台Windows/Linux/Mac</div>
                    <div class="tech-spec"><strong>部署便利:</strong> 本地化离线可用</div>
                    <div class="tech-spec"><strong>创新亮点:</strong> 5大技术创新突破</div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="chart-container">
            <div class="chart-title">📋 完整使用说明</div>
            <div class="description">
                <h4>🖼️ 如何保存图片</h4>
                <p><strong>方法1：</strong> 右键点击图表区域，选择"另存为图像"或"复制图像"</p>
                <p><strong>方法2：</strong> 使用浏览器截图功能（Ctrl+Shift+S 或 F12开发者工具）</p>
                <p><strong>方法3：</strong> 使用第三方截图工具（如Snipaste、微信截图等）</p>
                
                <h4>📁 建议图片命名</h4>
                <div class="tech-specs">
                    <div class="tech-spec">图表1: system-architecture.png</div>
                    <div class="tech-spec">图表2: security-mechanism.png</div>
                    <div class="tech-spec">图表3: rag-knowledge-base.png</div>
                    <div class="tech-spec">图表4: competition-extraction.png</div>
                    <div class="tech-spec">图表5: technical-achievements.png</div>
                </div>
                
                <h4>💾 保存位置</h4>
                <p>建议保存到项目的 <code>static/images/</code> 目录下，便于在PPT和文档中引用。</p>
                
                <h4>📊 图表用途</h4>
                <div class="tech-specs">
                    <div class="tech-spec"><strong>PPT第1页:</strong> 系统架构图 - 项目概述</div>
                    <div class="tech-spec"><strong>PPT第2页:</strong> 安全机制图 - 技术亮点</div>
                    <div class="tech-spec"><strong>PPT第3页:</strong> RAG流程图 - 知识库功能</div>
                    <div class="tech-spec"><strong>PPT第4页:</strong> 信息提取图 - 竞赛功能</div>
                    <div class="tech-spec"><strong>PPT第7页:</strong> 技术成果图 - 成果展示</div>
                </div>
            </div>
        </div>

        <div class="usage-note">
            <p><strong>✅ 图表生成完成！</strong></p>
            <p>所有5个图表已生成完毕，您可以按照上述说明保存图片并用于PPT制作。</p>
            <p><strong>下一步：</strong>参考项目中的PPT手册文档进行演示准备。</p>
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                fontFamily: 'Microsoft YaHei, Arial, sans-serif',
                fontSize: '14px'
            }
        });

        // 页面加载完成后的处理
        window.addEventListener('load', function() {
            console.log('智能助手项目图表第二部分已加载完成');
            
            // 添加右键菜单提示
            document.addEventListener('contextmenu', function(e) {
                if (e.target.closest('.mermaid')) {
                    console.log('右键点击图表区域 - 可以保存图片');
                }
            });
        });
    </script>
</body>
</html>
